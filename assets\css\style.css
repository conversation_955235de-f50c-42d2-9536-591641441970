.edit-config-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    margin: 0.1rem;
}

.config-actions {
    max-width: 300px;
}

.config-actions-cell {
    white-space: normal !important;
}

.config-info {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
}

.strings-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    background: #fafafa;
}

.string-input-group {
    margin-bottom: 0.5rem;
}

.string-input {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}



#fileContent {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.table-container {
    margin-top: 20px;
}

.header-section {
    background: #f8f9fa;
    padding: 20px 0;
    margin-bottom: 20px;
}
