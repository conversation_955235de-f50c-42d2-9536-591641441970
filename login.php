<?php
require_once 'includes/auth_config.php';
require_once 'includes/totp_auth.php';

$error = '';
$showQR = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'login':
                $totpCode = $_POST['totp_code'] ?? '';
                if (authenticateUser($totpCode)) {
                    header('Location: index.php');
                    exit;
                } else {
                    $error = 'Invalid TOTP code. Please try again.';
                }
                break;
                
            case 'show_qr':
                $showQR = true;
                break;
        }
    }
}

if (isAuthenticated()) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Project Configuration Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .totp-input {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
        }
        
        .qr-section {
            text-align: center;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .setup-instructions {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <h2 class="mb-0">
                <i class="fas fa-shield-alt"></i>
                Secure Login
            </h2>
            <p class="mb-0 mt-2">Project Configuration Manager</p>
        </div>
        
        <div class="login-body">
            <?php if ($error): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <input type="hidden" name="action" value="login">
                
                <div class="mb-3">
                    <label for="totp_code" class="form-label">
                        <i class="fas fa-mobile-alt"></i>
                        Enter 6-digit code from your authenticator app:
                    </label>
                    <input type="text" 
                           class="form-control totp-input" 
                           id="totp_code" 
                           name="totp_code" 
                           maxlength="6" 
                           pattern="[0-9]{6}" 
                           placeholder="000000"
                           autocomplete="off"
                           required>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 mb-3">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </button>
            </form>
            
            <div class="text-center">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="show_qr">
                    <button type="submit" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-qrcode"></i>
                        Setup Authenticator App
                    </button>
                </form>
            </div>
            
            <?php if ($showQR): ?>
                <div class="qr-section">
                    <h6>Setup Instructions:</h6>
                    <p class="small">Scan this QR code with your authenticator app:</p>
                    
                    <img src="<?php echo generateQRCodeURL(TOTP_SECRET, TOTP_ISSUER, TOTP_ACCOUNT); ?>" 
                         alt="QR Code" 
                         class="img-fluid mb-3">
                    
                    <div class="setup-instructions">
                        <strong>Manual Setup:</strong><br>
                        <small>
                            <strong>Secret Key:</strong> <code><?php echo TOTP_SECRET; ?></code><br>
                            <strong>Account:</strong> <?php echo TOTP_ACCOUNT; ?><br>
                            <strong>Issuer:</strong> <?php echo TOTP_ISSUER; ?>
                        </small>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Compatible with Google Authenticator, Authy, Microsoft Authenticator, and other TOTP apps.
                        </small>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('totp_code').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
            
            if (e.target.value.length === 6) {
                e.target.form.submit();
            }
        });
        
        document.getElementById('totp_code').focus();
    </script>
</body>
</html>
