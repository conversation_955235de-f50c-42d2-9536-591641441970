<?php
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    if ($bytes == 0) {
        return '0 B';
    }

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

function checkValidationFiles($directoryPath) {
    foreach (VALIDATION_FILES as $fileName) {
        $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;
        if (!file_exists($filePath)) {
            return false;
        }
    }
    return true;
}

function findConfigFiles($directoryPath) {
    $foundFiles = [];

    foreach (CONFIG_FILES as $displayName => $fileName) {
        $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

        if (file_exists($filePath)) {
            $foundFiles[] = [
                'display_name' => $displayName,
                'file_name' => $fileName,
                'full_path' => $filePath,
                'size' => filesize($filePath),
                'modified' => filemtime($filePath),
                'is_subdirectory' => strpos($fileName, '/') !== false || strpos($fileName, '\\') !== false
            ];
        }
    }

    return $foundFiles;
}

function isValidConfigFile($fileName) {
    return in_array($fileName, CONFIG_FILES);
}

function getConfigDisplayName($fileName) {
    $key = array_search($fileName, CONFIG_FILES);
    return $key !== false ? $key : basename($fileName);
}

function parseJsonStringArray($jsonContent) {
    $decoded = json_decode($jsonContent, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['success' => false, 'message' => 'Invalid JSON format: ' . json_last_error_msg()];
    }

    if (!is_array($decoded)) {
        return ['success' => false, 'message' => 'JSON content must be an array'];
    }

    $strings = [];
    foreach ($decoded as $item) {
        if (!is_string($item)) {
            return ['success' => false, 'message' => 'All array items must be strings'];
        }
        $strings[] = $item;
    }

    return ['success' => true, 'strings' => $strings];
}

function convertStringsToJson($strings) {
    $cleanStrings = [];
    foreach ($strings as $string) {
        $trimmed = trim($string);
        if ($trimmed !== '') {
            $cleanStrings[] = $trimmed;
        }
    }

    return json_encode($cleanStrings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

function getFileIcon($filename) {
    $extension = getFileExtension($filename);

    switch ($extension) {
        case 'txt':
            return 'fas fa-file-alt';
        case 'json':
            return 'fas fa-file-code';
        case 'ini':
        case 'cfg':
        case 'conf':
            return 'fas fa-cog';
        case 'md':
        case 'markdown':
            return 'fab fa-markdown';
        case 'sh':
        case 'bat':
        case 'cmd':
            return 'fas fa-terminal';
        case 'xml':
            return 'fas fa-file-code';
        case 'yml':
        case 'yaml':
            return 'fas fa-file-code';
        default:
            return 'fas fa-file';
    }
}
?>
