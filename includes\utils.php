<?php
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    if ($bytes == 0) {
        return '0 B';
    }

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

function findValidationFiles($directoryPath) {
    $foundFiles = [];

    foreach (VALIDATION_FILE_NAME as $fileName) {
        $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

        if (file_exists($filePath)) {
            $foundFiles[] = [
                'name' => $fileName,
                'path' => $fileName,
                'full_path' => $filePath,
                'size' => filesize($filePath),
                'modified' => filemtime($filePath),
                'is_subdirectory' => strpos($fileName, '/') !== false || strpos($fileName, '\\') !== false
            ];
        }
    }

    return $foundFiles;
}

function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

function getFileIcon($filename) {
    $extension = getFileExtension($filename);

    switch ($extension) {
        case 'txt':
            return 'fas fa-file-alt';
        case 'json':
            return 'fas fa-file-code';
        case 'ini':
        case 'cfg':
        case 'conf':
            return 'fas fa-cog';
        case 'md':
        case 'markdown':
            return 'fab fa-markdown';
        case 'sh':
        case 'bat':
        case 'cmd':
            return 'fas fa-terminal';
        case 'xml':
            return 'fas fa-file-code';
        case 'yml':
        case 'yaml':
            return 'fas fa-file-code';
        default:
            return 'fas fa-file';
    }
}
?>
