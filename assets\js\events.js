$(document).ready(function() {
    $('#refreshBtn').on('click', refreshData);
    $('#saveConfigBtn').on('click', saveConfig);
    $('#addStringBtn').on('click', addNewString);

    $(document).on('click', '.edit-config-btn', function() {
        const directory = $(this).data('directory');
        const fileName = $(this).data('file');
        const displayName = $(this).data('display-name');
        openConfigEditor(directory, fileName, displayName);
    });

    $(document).on('click', '.remove-string-btn', function() {
        removeString(this);
    });

    $(document).on('keypress', '.string-input', function(e) {
        if (e.which === 13) {
            addNewString();
        }
    });

    $('#configEditorModal').on('hidden.bs.modal', function() {
        currentDirectory = '';
        currentFileName = '';
        currentDisplayName = '';
        $('#stringsList').empty();
        $('.config-info').remove();
    });

    loadDirectories();
});
