$(document).ready(function() {
    $('#refreshBtn').on('click', refreshData);
    $('#saveFileBtn').on('click', saveFile);

    $(document).on('click', '.edit-file-btn', function() {
        const directory = $(this).data('directory');
        const fileName = $(this).data('file');
        const displayName = $(this).data('filename');
        openFileEditor(directory, fileName, displayName);
    });

    $('#fileEditorModal').on('hidden.bs.modal', function() {
        currentDirectory = '';
        currentFileName = '';
        $('#fileContent').val('');
        $('.file-info').remove();
    });

    loadDirectories();
});
