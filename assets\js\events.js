$(document).ready(function() {
    $('#refreshBtn').on('click', refreshData);
    $('#saveFileBtn').on('click', saveFile);

    $(document).on('click', '.modify-btn', function() {
        const directory = $(this).data('directory');
        const fileName = $(this).data('file');
        openFileEditor(directory, fileName);
    });

    $('#fileEditorModal').on('hidden.bs.modal', function() {
        currentDirectory = '';
        currentFileName = '';
        $('#fileContent').val('');
    });

    loadDirectories();
});
