$(document).ready(function() {
    $('#refreshBtn').on('click', refreshData);
    $('#saveFileBtn').on('click', saveFile);
    
    $(document).on('click', '.modify-btn', function() {
        const directory = $(this).data('directory');
        openFileEditor(directory, directory);
    });
    
    $('#fileEditorModal').on('hidden.bs.modal', function() {
        currentDirectory = '';
        $('#fileContent').val('');
    });
    
    loadDirectories();
});
