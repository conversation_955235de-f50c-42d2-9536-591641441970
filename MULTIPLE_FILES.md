# Multiple Validation Files Implementation

This document describes the implementation of multiple validation file support in the PHP File Manager project.

## Overview

The system has been enhanced to support multiple validation files per directory instead of a single file. This allows for more flexible project management where each directory can contain different types of configuration files.

## Configuration Changes

### Before (Single File)
```php
define('ROOT_PATH', '/home/<USER>/projects');
define('VALIDATION_FILE_NAME', 'project-timeline.txt');
```

### After (Multiple Files)
```php
define('ROOT_PATH', '/home/<USER>/projects');
define('VALIDATION_FILES', [
    'project-timeline.txt',
    'project-config.txt',
    'project-notes.txt'
]);
```

## Key Changes

### 1. Directory Scanning (`includes/directory_handler.php`)
- Now scans for multiple validation files per directory
- Only shows directories that contain at least one validation file
- Calculates total size of all validation files
- Creates individual buttons for each file found

### 2. File Operations (`includes/file_handler.php`)
- `getFileContent()` now requires both directory and fileName parameters
- `saveFileContent()` now requires directory, fileName, and content parameters
- Added validation to ensure only files in VALIDATION_FILES array can be accessed

### 3. AJAX Handler (`includes/ajax_handler.php`)
- Updated to pass fileName parameter to file operations
- Enhanced security by validating file names against allowed list

### 4. Frontend Changes

#### Table Structure
| Column | Content | Description |
|--------|---------|-------------|
| Directory Name | Project directory name | Directory containing validation files |
| Files | Number count | Total number of validation files found |
| Available Files | Comma-separated list | Names of all validation files present |
| Total Size | Combined file size | Total size of all validation files |
| Modified | Directory timestamp | Last modified date of directory |
| Actions | File buttons | Individual button for each validation file |

#### JavaScript Updates
- Added `currentFileName` global variable
- Updated modal functions to handle specific file names
- Modified event handlers to capture file-specific data
- Enhanced DataTable configuration for new columns

## User Interface

### Directory Listing
- Displays directories containing any validation files
- Shows count and list of available files
- Each validation file gets its own button in the Actions column

### File Editing
- Click any file button (e.g., "project-timeline.txt") to edit that specific file
- Modal title shows: "Edit: [filename] in [directory]"
- Each file is edited independently

### Button Layout
- Multiple small buttons per directory row
- Each button labeled with the file name
- Buttons are styled to fit compactly in the Actions column

## Security Enhancements

### File Validation
- Only files listed in VALIDATION_FILES array can be accessed
- Path validation prevents directory traversal
- File existence checks before read/write operations

### Input Sanitization
- Directory names are HTML-escaped in output
- File names are validated against whitelist
- All user inputs are sanitized

## Benefits

### Flexibility
- Support for different types of project files
- Easy to add new validation file types
- Each file can be edited independently

### Scalability
- System can handle any number of validation files
- New file types can be added by updating constants
- No code changes needed for new file types

### User Experience
- Clear indication of which files are available
- Direct access to specific files
- Intuitive file-specific editing

## File Path Structure

```
ROOT_PATH/
├── project_alpha/
│   ├── project-timeline.txt    ← Editable
│   ├── project-config.txt      ← Editable
│   ├── project-notes.txt       ← Editable
│   └── other-file.txt          ← Not accessible
├── project_beta/
│   ├── project-timeline.txt    ← Editable
│   └── project-config.txt      ← Editable
└── project_gamma/
    └── some-other-file.txt     ← Directory not shown (no validation files)
```

## API Changes

### AJAX Endpoints

#### Get File Content
```javascript
// Before
{action: 'get_file_content', directory: 'project_alpha'}

// After
{action: 'get_file_content', directory: 'project_alpha', fileName: 'project-timeline.txt'}
```

#### Save File
```javascript
// Before
{action: 'save_file', directory: 'project_alpha', content: '...'}

// After
{action: 'save_file', directory: 'project_alpha', fileName: 'project-timeline.txt', content: '...'}
```

## Implementation Notes

### Backward Compatibility
- The system maintains the same overall structure
- Existing directories with single files continue to work
- No migration needed for existing data

### Performance
- Efficient scanning of multiple files per directory
- Minimal overhead for additional file checks
- Optimized button generation for UI

### Maintenance
- Easy to add new validation file types
- Centralized configuration in constants file
- Clear separation of concerns maintained

This implementation provides a robust, scalable solution for managing multiple validation files while maintaining security and user experience standards.
