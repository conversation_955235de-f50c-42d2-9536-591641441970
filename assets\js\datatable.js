function initializeDataTable() {
    dataTable = $('#directoryTable').DataTable({
        ajax: {
            url: 'index.php',
            type: 'POST',
            data: {action: 'list_directories'},
            dataSrc: 'data'
        },
        columns: [
            {data: 'directory'},
            {data: 'file_count', title: 'Files'},
            {data: 'total_size', title: 'Total Size'},
            {data: 'modified'},
            {data: 'actions', orderable: false}
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true,
        columnDefs: [
            {
                targets: 4,
                className: 'file-actions-cell'
            }
        ],
        language: {
            emptyTable: "No directories found containing validation files",
            zeroRecords: "No matching directories found"
        }
    });
}

function loadDirectories() {
    if (dataTable) {
        dataTable.ajax.reload();
    } else {
        initializeDataTable();
    }
}
