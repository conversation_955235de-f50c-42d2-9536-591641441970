function initializeDataTable() {
    dataTable = $('#directoryTable').DataTable({
        ajax: {
            url: 'index.php',
            type: 'POST',
            data: {action: 'list_directories'},
            dataSrc: 'data'
        },
        columns: [
            {data: 'directory'},
            {data: 'file_count'},
            {data: 'files'},
            {data: 'total_size'},
            {data: 'modified'},
            {data: 'actions', orderable: false}
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            emptyTable: "No directories found containing validation files",
            zeroRecords: "No matching directories found"
        }
    });
}

function loadDirectories() {
    if (dataTable) {
        dataTable.ajax.reload();
    } else {
        initializeDataTable();
    }
}
