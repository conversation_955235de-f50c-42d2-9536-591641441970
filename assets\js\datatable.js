function initializeDataTable() {
    dataTable = $('#directoryTable').DataTable({
        ajax: {
            url: 'index.php',
            type: 'POST',
            data: {action: 'list_directories'},
            dataSrc: 'data'
        },
        columns: [
            {data: 'directory', title: 'Directory'},
            {data: 'validation_files', title: 'Validation Files', orderable: false},
            {data: 'config_count', title: 'Config Files'},
            {data: 'total_size', title: 'Total Size'},
            {data: 'modified', title: 'Modified'},
            {data: 'actions', title: 'Config Actions', orderable: false}
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        responsive: true,
        columnDefs: [
            {
                targets: [1, 5],
                className: 'config-actions-cell'
            },
            {
                targets: 1,
                className: 'validation-files-cell'
            }
        ],
        language: {
            emptyTable: "No directories found with required validation files",
            zeroRecords: "No matching directories found"
        }
    });
}

function loadDirectories() {
    if (dataTable) {
        dataTable.ajax.reload();
    } else {
        initializeDataTable();
    }
}
