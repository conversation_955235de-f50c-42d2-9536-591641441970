function initializeDataTable() {
    dataTable = $('#directoryTable').DataTable({
        ajax: {
            url: 'index.php',
            type: 'POST',
            data: {action: 'list_directories'},
            dataSrc: 'data'
        },
        columns: [
            {data: 'directory', title: 'Directory'},
            {data: 'total_size', title: 'Total Size'},
            {data: 'modified', title: 'Modified'},
            {data: 'actions', title: 'Config Actions', orderable: false}
        ],
        order: [[2, 'desc']],
        pageLength: 25,
        responsive: true,
        columnDefs: [
            {
                targets: 3,
                className: 'config-actions-cell'
            }
        ],
        language: {
            emptyTable: "No directories found with required validation files",
            zeroRecords: "No matching directories found"
        }
    });
}

function loadDirectories() {
    if (dataTable) {
        dataTable.ajax.reload();
    } else {
        initializeDataTable();
    }
}
