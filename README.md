# PHP File Manager - Project File Editor

A single-page file management system built with PHP, HTML, Bootstrap CSS, and DataTables. This application lists only directories containing validation files and allows editing those files through a modal dialog with AJAX functionality.

## Features

- **Filtered Directory Listing**: Displays only directories in the root path that contain validation files
- **Multiple File Support**: Each directory can contain multiple validation files
- **File Editing**: Modal dialog for editing individual validation files with AJAX loading
- **Real-time Refresh**: Full AJAX functionality with refresh capability
- **Security**: Path validation and file restriction for security
- **Responsive Design**: Bootstrap-based responsive interface
- **DataTables Integration**: Searchable, sortable, and paginated directory listing

## Configuration

The application uses the following constants defined in `includes/constants.php`:

```php
define('ROOT_PATH', '/home/<USER>/projects');
define('VALIDATION_FILES', [
    'project-timeline.txt',
    'project-config.txt',
    'project-notes.txt'
]);
```

## Technology Stack

- **Backend**: PHP
- **Frontend**: HTML5, Bootstrap 5.3.0, DataTables 1.13.6
- **JavaScript**: jQuery 3.7.1
- **Icons**: Font Awesome 6.4.0
- **Architecture**: Single-page application (SPA)
- **Dependencies**: All CSS/JS loaded from CDN

## File Structure

```
project/
├── index.php                    # Main application entry point
├── includes/                    # PHP backend files
│   ├── config.php              # Configuration constants
│   ├── functions.php           # Main functions loader
│   ├── ajax_handler.php        # AJAX request handler
│   ├── security.php            # Security functions
│   ├── directory_handler.php   # Directory operations
│   ├── file_handler.php        # File operations
│   └── utils.php               # Utility functions
├── templates/                   # HTML template files
│   ├── header.php              # HTML head and opening tags
│   ├── styles.php              # CSS includes
│   ├── main_content.php        # Main page content
│   ├── modal.php               # File editor modal
│   ├── scripts.php             # JavaScript includes
│   └── footer.php              # Scripts and closing tags
├── assets/                      # Static assets
│   ├── css/
│   │   └── style.css           # Custom CSS styles
│   └── js/                     # JavaScript files
│       ├── app.js              # Global variables
│       ├── datatable.js        # DataTable functionality
│       ├── modal.js            # Modal operations
│       ├── utils.js            # Utility functions
│       └── events.js           # Event handlers and initialization
├── README.md                    # This documentation file
└── START.md                     # Project requirements and specifications
```

## Installation

1. Clone or download the project files
2. Place `index.php` in your web server directory
3. Update the `ROOT_PATH` constant in `index.php` to point to your desired directory
4. Ensure the web server has read/write permissions to the directories and files
5. Access the application through your web browser

## Usage

### Directory Listing
- The main page displays a table of all directories that contain validation files
- Each row shows:
  - Directory name
  - Number of validation files found
  - List of available files
  - Total size of all validation files
  - Last modified date
  - Actions (individual buttons for each file)

### Editing Files
1. Click any file button (e.g., "project-timeline.txt") for a directory
2. A modal dialog opens with the current file content
3. The modal title shows which file is being edited
4. Edit the content in the textarea
5. Click "Save Changes" to save or "Cancel" to discard changes

### Refreshing Data
- Click the "Refresh" button in the top-right corner to rescan directories
- The table will update to show any new directories with validation files

## Security Features

- **Path Validation**: Prevents directory traversal attacks
- **File Restriction**: Only allows editing of the specified validation file
- **Input Sanitization**: All user inputs are properly sanitized
- **Permission Checks**: Validates file and directory permissions before operations

## API Endpoints

The application handles the following AJAX requests:

- `POST action=list_directories` - Returns JSON array of directories containing validation file
- `POST action=get_file_content` - Returns validation file content from specified directory
- `POST action=save_file` - Saves content to validation file and returns status
- `POST action=refresh` - Refreshes directory listing

## Error Handling

- File not found errors
- Permission denied errors
- Network errors with retry capability
- Input validation failures
- User-friendly error messages without exposing system paths

## Browser Compatibility

- Modern browsers supporting ES6+
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Development

The application is organized into modular components:

### PHP Backend (`includes/`)
- **config.php**: Application constants and configuration
- **security.php**: Path validation and security functions
- **directory_handler.php**: Directory scanning and listing
- **file_handler.php**: File read/write operations
- **utils.php**: Utility functions like formatBytes
- **ajax_handler.php**: AJAX request routing and responses

### Frontend Templates (`templates/`)
- **header.php**: HTML document head and opening body
- **main_content.php**: Main page layout and table structure
- **modal.php**: File editor modal dialog
- **footer.php**: Script includes and closing tags

### Assets (`assets/`)
- **css/style.css**: Custom styling
- **js/app.js**: Global variables and state
- **js/datatable.js**: DataTable initialization and management
- **js/modal.js**: Modal operations and file editing
- **js/utils.js**: Utility functions and alerts
- **js/events.js**: Event handlers and application initialization

## Troubleshooting

### Common Issues

1. **No directories shown**: 
   - Check if `ROOT_PATH` exists and is readable
   - Verify directories contain the validation file
   - Check web server permissions

2. **Cannot edit files**:
   - Ensure web server has write permissions
   - Check if validation files exist and are writable
   - Verify path validation is not blocking access

3. **AJAX errors**:
   - Check browser console for JavaScript errors
   - Verify CDN resources are loading correctly
   - Check network connectivity

### Debug Mode

To enable debug mode, you can add error reporting at the top of `index.php`:

```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## License

This project is open source and available under the MIT License.

## Support

For issues or questions, please check the troubleshooting section above or review the `START.md` file for detailed implementation requirements.
