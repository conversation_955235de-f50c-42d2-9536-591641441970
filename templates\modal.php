<div class="modal fade" id="configEditorModal" tabindex="-1" aria-labelledby="configEditorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configEditorModalLabel">Edit Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body position-relative">
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label class="form-label mb-0">Configuration Strings:</label>
                        <button type="button" id="addStringBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus"></i> Add String
                        </button>
                    </div>
                    <div id="stringsList" class="strings-container">
                    </div>
                </div>

                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle"></i>
                        Each line represents a string in the JSON array. Press Enter to add a new string.
                        Empty strings will be automatically removed when saving.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="saveConfigBtn" class="btn btn-success">
                    <i class="fas fa-save"></i> Save Configuration
                </button>
            </div>
        </div>
    </div>
</div>
