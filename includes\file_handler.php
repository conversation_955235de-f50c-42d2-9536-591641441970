<?php
function getFileContent($directory, $fileName) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!isValidationFile($fileName)) {
        return ['success' => false, 'message' => 'File not allowed for editing'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'File not found'];
    }

    if (!validatePath($filePath)) {
        return ['success' => false, 'message' => 'Invalid file path'];
    }

    $content = file_get_contents($filePath);

    if ($content === false) {
        return ['success' => false, 'message' => 'Unable to read file'];
    }

    return [
        'success' => true,
        'content' => $content,
        'filename' => $fileName,
        'size' => filesize($filePath),
        'modified' => date('Y-m-d H:i:s', filemtime($filePath))
    ];
}

function saveFileContent($directory, $fileName, $content) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!isValidationFile($fileName)) {
        return ['success' => false, 'message' => 'File not allowed for editing'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'File not found'];
    }

    if (!validatePath($filePath)) {
        return ['success' => false, 'message' => 'Invalid file path'];
    }

    $fileDir = dirname($filePath);
    if (!is_dir($fileDir)) {
        if (!mkdir($fileDir, 0755, true)) {
            return ['success' => false, 'message' => 'Unable to create directory'];
        }
    }

    $result = file_put_contents($filePath, $content);

    if ($result === false) {
        return ['success' => false, 'message' => 'Unable to save file'];
    }

    return [
        'success' => true,
        'message' => 'File saved successfully',
        'filename' => $fileName,
        'size' => $result
    ];
}

function isValidationFile($fileName) {
    return in_array($fileName, VALIDATION_FILE_NAME);
}
?>
