<?php
function getFileContent($directory, $fileName) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!in_array($fileName, VALIDATION_FILES)) {
        return ['success' => false, 'message' => 'Invalid file name'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'File not found'];
    }

    $content = file_get_contents($filePath);

    if ($content === false) {
        return ['success' => false, 'message' => 'Unable to read file'];
    }

    return ['success' => true, 'content' => $content, 'fileName' => $fileName];
}

function saveFileContent($directory, $fileName, $content) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!in_array($fileName, VALIDATION_FILES)) {
        return ['success' => false, 'message' => 'Invalid file name'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'File not found'];
    }

    $result = file_put_contents($filePath, $content);

    if ($result === false) {
        return ['success' => false, 'message' => 'Unable to save file'];
    }

    return ['success' => true, 'message' => 'File saved successfully'];
}
?>
