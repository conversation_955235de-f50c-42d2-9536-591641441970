<?php
function getConfigFileContent($directory, $fileName) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!checkValidationFiles($directoryPath)) {
        return ['success' => false, 'message' => 'Directory does not contain required validation files'];
    }

    if (!isValidConfigFile($fileName)) {
        return ['success' => false, 'message' => 'File not allowed for editing'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Config file not found'];
    }

    if (!validatePath($filePath)) {
        return ['success' => false, 'message' => 'Invalid file path'];
    }

    $content = file_get_contents($filePath);

    if ($content === false) {
        return ['success' => false, 'message' => 'Unable to read file'];
    }

    $parseResult = parseJsonStringArray($content);

    if (!$parseResult['success']) {
        return $parseResult;
    }

    return [
        'success' => true,
        'strings' => $parseResult['strings'],
        'display_name' => getConfigDisplayName($fileName),
        'filename' => $fileName,
        'size' => filesize($filePath),
        'modified' => date('Y-m-d H:i:s', filemtime($filePath))
    ];
}

function saveConfigFileContent($directory, $fileName, $strings) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }

    if (!checkValidationFiles($directoryPath)) {
        return ['success' => false, 'message' => 'Directory does not contain required validation files'];
    }

    if (!isValidConfigFile($fileName)) {
        return ['success' => false, 'message' => 'File not allowed for editing'];
    }

    $filePath = $directoryPath . DIRECTORY_SEPARATOR . $fileName;

    if (!validatePath($filePath)) {
        return ['success' => false, 'message' => 'Invalid file path'];
    }

    $fileDir = dirname($filePath);
    if (!is_dir($fileDir)) {
        if (!mkdir($fileDir, 0755, true)) {
            return ['success' => false, 'message' => 'Unable to create directory'];
        }
    }

    $jsonContent = convertStringsToJson($strings);

    if ($jsonContent === false) {
        return ['success' => false, 'message' => 'Unable to convert strings to JSON'];
    }

    $result = file_put_contents($filePath, $jsonContent);

    if ($result === false) {
        return ['success' => false, 'message' => 'Unable to save file'];
    }

    return [
        'success' => true,
        'message' => 'Configuration saved successfully',
        'display_name' => getConfigDisplayName($fileName),
        'filename' => $fileName,
        'size' => $result,
        'string_count' => count($strings)
    ];
}
?>
