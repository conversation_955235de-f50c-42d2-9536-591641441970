<?php
function getFileContent($directory) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;
    
    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }
    
    $filePath = $directoryPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
    
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Validation file not found'];
    }
    
    $content = file_get_contents($filePath);
    
    if ($content === false) {
        return ['success' => false, 'message' => 'Unable to read file'];
    }
    
    return ['success' => true, 'content' => $content];
}

function saveFileContent($directory, $content) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;
    
    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }
    
    $filePath = $directoryPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
    
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Validation file not found'];
    }
    
    $result = file_put_contents($filePath, $content);
    
    if ($result === false) {
        return ['success' => false, 'message' => 'Unable to save file'];
    }
    
    return ['success' => true, 'message' => 'File saved successfully'];
}
?>
