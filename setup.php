<?php
require_once 'includes/auth_config.php';
require_once 'includes/totp_auth.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOTP Setup - Project Configuration Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .setup-body {
            padding: 2rem;
        }
        
        .qr-container {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .secret-key {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            margin: 1rem 0;
        }
        
        .step {
            margin-bottom: 2rem;
            padding: 1rem;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-card">
            <div class="setup-header">
                <h2 class="mb-0">
                    <i class="fas fa-shield-alt"></i>
                    TOTP Authentication Setup
                </h2>
                <p class="mb-0 mt-2">Project Configuration Manager</p>
            </div>
            
            <div class="setup-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>First Time Setup:</strong> Follow these steps to configure your authenticator app.
                </div>
                
                <div class="step">
                    <h5><i class="fas fa-mobile-alt"></i> Step 1: Install Authenticator App</h5>
                    <p>Download one of these apps on your mobile device:</p>
                    <ul>
                        <li><strong>Google Authenticator</strong> (iOS/Android)</li>
                        <li><strong>Microsoft Authenticator</strong> (iOS/Android)</li>
                        <li><strong>Authy</strong> (iOS/Android/Desktop)</li>
                        <li><strong>1Password</strong> (Premium feature)</li>
                    </ul>
                </div>
                
                <div class="step">
                    <h5><i class="fas fa-qrcode"></i> Step 2: Scan QR Code</h5>
                    <p>Open your authenticator app and scan this QR code:</p>
                    
                    <div class="qr-container">
                        <img src="<?php echo generateQRCodeURL(TOTP_SECRET, TOTP_ISSUER, TOTP_ACCOUNT); ?>" 
                             alt="TOTP QR Code" 
                             class="img-fluid">
                    </div>
                </div>
                
                <div class="step">
                    <h5><i class="fas fa-keyboard"></i> Step 3: Manual Setup (Alternative)</h5>
                    <p>If you can't scan the QR code, manually enter these details:</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Account:</strong><br>
                            <code><?php echo TOTP_ACCOUNT; ?></code>
                        </div>
                        <div class="col-md-6">
                            <strong>Issuer:</strong><br>
                            <code><?php echo TOTP_ISSUER; ?></code>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <strong>Secret Key:</strong>
                        <div class="secret-key">
                            <?php echo TOTP_SECRET; ?>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <strong>Settings:</strong>
                        <ul>
                            <li>Type: Time-based (TOTP)</li>
                            <li>Algorithm: SHA1</li>
                            <li>Digits: 6</li>
                            <li>Period: 30 seconds</li>
                        </ul>
                    </div>
                </div>
                
                <div class="step">
                    <h5><i class="fas fa-check-circle"></i> Step 4: Test Login</h5>
                    <p>Once configured, your app will show a 6-digit code that changes every 30 seconds. Use this code to log in.</p>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Important:</strong> Save your secret key in a secure location. If you lose access to your authenticator app, you'll need this key to reconfigure it.
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="login.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt"></i>
                        Go to Login
                    </a>
                </div>
                
                <div class="mt-4">
                    <h6>Security Notes:</h6>
                    <ul class="small text-muted">
                        <li>TOTP codes are valid for 30 seconds and can only be used once</li>
                        <li>The system allows a 30-second tolerance for clock differences</li>
                        <li>Sessions expire after 1 hour of inactivity</li>
                        <li>Always log out when finished to maintain security</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
