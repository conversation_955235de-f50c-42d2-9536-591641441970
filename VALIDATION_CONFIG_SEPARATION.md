# Validation vs Config Files Separation Update

## Overview
The system has been completely restructured to separate validation files (which must exist to validate a directory) from config files (which are editable JSON string arrays).

## Key Changes

### 1. New Constants Structure (`includes/constants.php`)

#### Validation Files (Must Exist)
```php
define('VALIDATION_FILES', [
    'project-timeline.txt',
    'data/config.json'
]);
```
These files must exist in a directory for it to be considered valid and shown in the listing.

#### Config Files (Editable JSON Arrays)
```php
define('CONFIG_FILES', [
    'Config 1' => 'subdir/json-config-file-1.json',
    'Config 2' => 'subdir2/json-config-file-2.json',
    'Main Settings' => 'settings/main.json',
    'User Preferences' => 'config/user-prefs.json'
]);
```
These files contain JSON arrays of strings and can be edited through the interface.

### 2. JSON String Array Format

#### Expected Format
```json
[
  "string 1",
  "string 2", 
  "string 3"
]
```

#### Features
- Each config file contains a JSON array of strings
- Files can have any extension but content must be valid JSON
- Empty strings are automatically filtered out
- Pretty-printed JSON with proper formatting

### 3. New Functionality

#### Directory Validation
- Directories are only shown if ALL validation files exist
- Validation files are displayed in a separate column
- Config files are shown as editable buttons

#### Config File Editing
- Special modal interface for JSON string array editing
- Line-by-line string input with add/remove functionality
- Real-time validation and formatting
- Automatic JSON conversion

#### Enhanced UI
- **Validation Files Column**: Shows which validation files exist
- **Config Actions Column**: Individual edit buttons for each config
- **String Editor**: Dedicated interface for managing string arrays

### 4. Updated DataTable Structure

| Column | Content | Purpose |
|--------|---------|---------|
| Directory | Directory name | Project identification |
| Validation Files | List of existing validation files | Shows compliance status |
| Configs | Count of available config files | Quick overview |
| Size | Total size of config files | Storage information |
| Modified | Latest modification date | Change tracking |
| Config Actions | Edit buttons for each config | Direct access to editing |

### 5. New Modal Interface

#### String Array Editor Features
- **Add String**: Button and Enter key support
- **Remove String**: Individual delete buttons
- **Auto-numbering**: Visual line numbers
- **Validation**: Real-time feedback
- **Info Panel**: File details and statistics

#### User Experience
- Modal title shows config display name (e.g., "Config 1")
- File information panel with size and modification date
- Intuitive string management with visual feedback
- Automatic JSON formatting and validation

### 6. Backend Enhancements

#### New Functions
- `checkValidationFiles()` - Validates directory has required files
- `findConfigFiles()` - Locates editable config files
- `parseJsonStringArray()` - Parses and validates JSON arrays
- `convertStringsToJson()` - Converts string array to formatted JSON
- `isValidConfigFile()` - Security validation for config files
- `getConfigDisplayName()` - Gets friendly name for config files

#### Enhanced Security
- Separate validation for config vs validation files
- JSON format validation
- Path security for subdirectory configs
- Whitelist-based file access

### 7. Example Usage

#### Directory Structure
```
/home/<USER>/projects/
├── project-alpha/
│   ├── project-timeline.txt        ✓ Validation file
│   ├── data/config.json           ✓ Validation file
│   ├── subdir/json-config-file-1.json    ← Editable config
│   ├── settings/main.json         ← Editable config
│   └── other-file.txt             ✗ Ignored
├── project-beta/
│   ├── project-timeline.txt        ✓ Validation file
│   └── data/config.json           ✓ Validation file (missing configs)
```

#### UI Display
**project-alpha**:
- **Validation Files**: ✓ project-timeline.txt, ✓ data/config.json
- **Configs**: 2
- **Actions**: [⚙️ Config 1] [⚙️ Main Settings]

**project-beta**:
- **Validation Files**: ✓ project-timeline.txt, ✓ data/config.json  
- **Configs**: 0
- **Actions**: (no config files found)

### 8. Configuration Management

#### Adding New Config Types
```php
define('CONFIG_FILES', [
    'Config 1' => 'subdir/json-config-file-1.json',
    'Config 2' => 'subdir2/json-config-file-2.json',
    'API Settings' => 'api/endpoints.json',        // New
    'User Roles' => 'security/roles.json'          // New
]);
```

#### Adding New Validation Requirements
```php
define('VALIDATION_FILES', [
    'project-timeline.txt',
    'data/config.json',
    'README.md'                                     // New requirement
]);
```

### 9. Benefits

#### Clear Separation of Concerns
- Validation files ensure directory compliance
- Config files provide editable functionality
- No confusion between validation and editing

#### Enhanced User Experience
- Intuitive string array editing
- Visual feedback for validation status
- Friendly config names instead of file paths

#### Improved Security
- Separate validation logic
- JSON format enforcement
- Controlled file access

#### Scalability
- Easy to add new config types
- Flexible validation requirements
- Modular architecture

### 10. Migration Notes

#### From Previous Version
- Old `VALIDATION_FILE_NAME` is now split into two constants
- File editing now specifically targets JSON string arrays
- UI completely redesigned for new workflow

#### Backward Compatibility
- None - this is a breaking change requiring configuration update
- All existing file references need to be updated

### 11. Testing Checklist
- [ ] Directories with all validation files appear in listing
- [ ] Directories missing validation files are hidden
- [ ] Config edit buttons work for each configured file
- [ ] String array editor loads existing JSON correctly
- [ ] Adding/removing strings works properly
- [ ] JSON validation prevents invalid saves
- [ ] Subdirectory config files work correctly
- [ ] File information displays accurately
- [ ] Security validation prevents unauthorized access
- [ ] Empty strings are filtered out on save

The system now provides a robust separation between validation requirements and editable configuration management, with a specialized interface for JSON string array editing.
