# TOTP Authentication Implementation

## Overview
The system now includes Time-based One-Time Password (TOTP) authentication using authenticator apps like Google Authenticator, providing secure access without requiring a database.

## Key Features

### 1. Database-Free Authentication
- **No Database Required**: TOTP secret stored in configuration file
- **Session Management**: PHP sessions for login state tracking
- **Secure Storage**: Secret key in server-side configuration

### 2. TOTP Implementation
- **Standard Compliance**: RFC 6238 compliant TOTP implementation
- **30-Second Intervals**: Standard time window for code generation
- **Clock Tolerance**: ±30 seconds tolerance for clock drift
- **6-Digit Codes**: Standard authenticator app format

### 3. Multi-App Support
- **Google Authenticator**: Full compatibility
- **Microsoft Authenticator**: Full compatibility
- **Authy**: Full compatibility
- **1Password**: Premium feature support
- **Any RFC 6238 App**: Standard TOTP implementation

## File Structure

### New Authentication Files
```
├── includes/
│   ├── auth_config.php         # TOTP configuration
│   └── totp_auth.php          # TOTP functions
├── login.php                   # Login interface
├── logout.php                  # Logout page
└── setup.php                   # Setup instructions
```

### Configuration (`includes/auth_config.php`)
```php
define('TOTP_SECRET', 'JBSWY3DPEHPK3PXP');     // Base32 secret key
define('TOTP_ISSUER', 'Project Config Manager'); // App identifier
define('TOTP_ACCOUNT', 'admin@localhost');       // Account identifier
define('SESSION_TIMEOUT', 3600);                 // 1 hour timeout
```

## Core Functions

### 1. TOTP Generation (`totp_auth.php`)
```php
function generateTOTP($secret, $timeSlice = null)
```
- Generates 6-digit TOTP code
- Uses HMAC-SHA1 algorithm
- 30-second time windows

### 2. TOTP Verification
```php
function verifyTOTP($secret, $code, $tolerance = 1)
```
- Validates user-provided code
- Checks current and adjacent time windows
- Prevents replay attacks

### 3. Session Management
```php
function isAuthenticated()
function authenticateUser($totpCode)
function requireAuth()
```

## Security Features

### 1. Session Security
- **Timeout Protection**: Sessions expire after 1 hour
- **Secure Logout**: Complete session destruction
- **Authentication Checks**: All pages and AJAX endpoints protected

### 2. TOTP Security
- **Time-Based**: Codes expire every 30 seconds
- **One-Time Use**: Each code valid for single use
- **Clock Tolerance**: Handles minor time differences
- **Secure Secret**: Base32 encoded secret key

### 3. Access Control
- **Page Protection**: All main pages require authentication
- **AJAX Protection**: All API endpoints check authentication
- **Automatic Redirect**: Unauthenticated users redirected to login

## User Interface

### 1. Login Page (`login.php`)
- **Clean Design**: Professional login interface
- **QR Code Setup**: Built-in authenticator app setup
- **Auto-Submit**: Automatic form submission when 6 digits entered
- **Error Handling**: Clear feedback for invalid codes

### 2. Setup Page (`setup.php`)
- **Step-by-Step Guide**: Complete setup instructions
- **QR Code Display**: Visual setup for authenticator apps
- **Manual Setup**: Alternative text-based configuration
- **Multiple App Support**: Instructions for various authenticator apps

### 3. Main Interface Updates
- **Logout Button**: Prominent logout option in header
- **Session Awareness**: Automatic redirect on session expiry
- **Seamless Integration**: Authentication transparent to normal workflow

## Setup Process

### 1. Initial Configuration
1. **Access Setup Page**: Visit `setup.php`
2. **Install Authenticator App**: Download preferred app
3. **Scan QR Code**: Use app to scan displayed QR code
4. **Test Login**: Verify setup with generated code

### 2. Manual Configuration
If QR scanning isn't available:
- **Secret Key**: `JBSWY3DPEHPK3PXP`
- **Account**: `admin@localhost`
- **Issuer**: `Project Config Manager`
- **Type**: Time-based (TOTP)
- **Algorithm**: SHA1
- **Digits**: 6
- **Period**: 30 seconds

## Usage Workflow

### 1. Login Process
1. **Access Application**: Navigate to main URL
2. **Redirect to Login**: Automatic redirect if not authenticated
3. **Enter TOTP Code**: Input 6-digit code from authenticator app
4. **Access Granted**: Redirect to main application

### 2. Session Management
- **Active Session**: 1-hour timeout from last activity
- **Automatic Logout**: Session expires after timeout
- **Manual Logout**: Click logout button for immediate termination

### 3. Security Best Practices
- **Regular Logout**: Log out when finished
- **Secure Device**: Keep authenticator device secure
- **Backup Secret**: Store secret key securely for recovery

## Technical Implementation

### 1. Base32 Decoding
```php
function base32Decode($data)
```
- Converts Base32 secret to binary
- Handles standard Base32 alphabet
- Removes padding and whitespace

### 2. HMAC-SHA1 Implementation
- Uses PHP's `hash_hmac()` function
- Time-based counter (Unix timestamp / 30)
- Dynamic truncation for 6-digit output

### 3. JavaScript Integration
- **Auto-Submit**: Form submits when 6 digits entered
- **Input Validation**: Only numeric input allowed
- **Focus Management**: Automatic focus on code input
- **AJAX Protection**: Authentication checks in all AJAX calls

## Customization Options

### 1. Configuration Changes
```php
// Change session timeout (seconds)
define('SESSION_TIMEOUT', 7200);  // 2 hours

// Change app identification
define('TOTP_ISSUER', 'My Custom App');
define('TOTP_ACCOUNT', '<EMAIL>');
```

### 2. Secret Key Generation
To generate a new secret key:
```php
// Generate random 16-byte secret
$secret = '';
for ($i = 0; $i < 16; $i++) {
    $secret .= chr(random_int(0, 255));
}
$base32Secret = base32_encode($secret);
```

### 3. UI Customization
- **Login Page**: Modify `login.php` for custom branding
- **Setup Page**: Update `setup.php` for custom instructions
- **Styling**: Customize CSS for brand consistency

## Troubleshooting

### 1. Common Issues
- **Time Sync**: Ensure server and device clocks are synchronized
- **Code Timing**: Enter codes quickly (30-second validity)
- **App Setup**: Verify correct secret key entry in authenticator app

### 2. Recovery Options
- **Secret Key Backup**: Use stored secret to reconfigure authenticator
- **Server Access**: Modify `auth_config.php` to change/disable authentication
- **Session Reset**: Clear PHP sessions to force re-authentication

### 3. Security Considerations
- **Secret Protection**: Keep TOTP secret secure and private
- **HTTPS Required**: Use HTTPS in production for secure transmission
- **Regular Updates**: Keep authenticator apps updated

This implementation provides enterprise-grade security without database complexity, making it ideal for standalone applications requiring secure access control.
