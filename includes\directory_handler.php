<?php
function getDirectoryListing() {
    $directories = [];

    if (!is_dir(ROOT_PATH)) {
        return $directories;
    }

    $items = scandir(ROOT_PATH);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $fullPath = ROOT_PATH . DIRECTORY_SEPARATOR . $item;

        if (is_dir($fullPath)) {
            $validationFiles = [];
            $totalSize = 0;

            foreach (VALIDATION_FILES as $validationFile) {
                $validationFilePath = $fullPath . DIRECTORY_SEPARATOR . $validationFile;
                if (file_exists($validationFilePath)) {
                    $fileSize = filesize($validationFilePath);
                    $validationFiles[] = [
                        'name' => $validationFile,
                        'size' => $fileSize,
                        'path' => $validationFilePath
                    ];
                    $totalSize += $fileSize;
                }
            }

            if (!empty($validationFiles)) {
                $modified = filemtime($fullPath);
                $fileButtons = '';

                foreach ($validationFiles as $file) {
                    $fileButtons .= '<button class="btn btn-sm btn-warning modify-btn me-1 mb-1" data-directory="' . htmlspecialchars($item) . '" data-file="' . htmlspecialchars($file['name']) . '">' . htmlspecialchars($file['name']) . '</button>';
                }

                $directories[] = [
                    'directory' => $item,
                    'file_count' => count($validationFiles),
                    'total_size' => formatBytes($totalSize),
                    'files' => implode(', ', array_column($validationFiles, 'name')),
                    'modified' => date('Y-m-d H:i:s', $modified),
                    'actions' => $fileButtons
                ];
            }
        }
    }

    return $directories;
}
?>
