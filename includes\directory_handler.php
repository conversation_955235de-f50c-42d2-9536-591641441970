<?php
function getDirectoryListing() {
    $directories = [];
    
    if (!is_dir(ROOT_PATH)) {
        return $directories;
    }
    
    $items = scandir(ROOT_PATH);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $fullPath = ROOT_PATH . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($fullPath)) {
            $validationFilePath = $fullPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
            
            if (file_exists($validationFilePath)) {
                $fileSize = filesize($validationFilePath);
                $modified = filemtime($fullPath);
                
                $directories[] = [
                    'directory' => $item,
                    'validation_size' => formatBytes($fileSize),
                    'modified' => date('Y-m-d H:i:s', $modified),
                    'actions' => '<button class="btn btn-sm btn-warning modify-btn" data-directory="' . htmlspecialchars($item) . '">Modify Config</button>'
                ];
            }
        }
    }
    
    return $directories;
}
?>
