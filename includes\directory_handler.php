<?php
function getDirectoryListing() {
    $directories = [];

    if (!is_dir(ROOT_PATH)) {
        return $directories;
    }

    $items = scandir(ROOT_PATH);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $fullPath = ROOT_PATH . DIRECTORY_SEPARATOR . $item;

        if (is_dir($fullPath)) {
            if (checkValidationFiles($fullPath)) {
                $configFiles = findConfigFiles($fullPath);

                $totalSize = 0;
                $latestModified = 0;
                $fileButtons = [];

                foreach ($configFiles as $file) {
                    $totalSize += $file['size'];
                    $latestModified = max($latestModified, $file['modified']);

                    $fileButtons[] = '<button class="btn btn-sm btn-outline-success me-1 mb-1 edit-config-btn"
                                        data-directory="' . htmlspecialchars($item) . '"
                                        data-file="' . htmlspecialchars($file['file_name']) . '"
                                        data-display-name="' . htmlspecialchars($file['display_name']) . '"
                                        title="Edit ' . htmlspecialchars($file['display_name']) . '">
                                        <i class="fas fa-cog"></i> ' . htmlspecialchars($file['display_name']) . '
                                      </button>';
                }

                $validationInfo = [];
                foreach (VALIDATION_FILES as $validationFile) {
                    $validationPath = $fullPath . DIRECTORY_SEPARATOR . $validationFile;
                    if (file_exists($validationPath)) {
                        $validationInfo[] = '<small class="text-muted">✓ ' . htmlspecialchars($validationFile) . '</small>';
                    }
                }

                $directories[] = [
                    'directory' => $item,
                    'validation_files' => '<div class="validation-files">' . implode('<br>', $validationInfo) . '</div>',
                    'config_count' => count($configFiles),
                    'total_size' => formatBytes($totalSize),
                    'modified' => date('Y-m-d H:i:s', $latestModified),
                    'actions' => '<div class="config-actions">' . implode('', $fileButtons) . '</div>'
                ];
            }
        }
    }

    return $directories;
}

function getDirectoryConfigFiles($directory) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath) || !is_dir($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory'];
    }

    if (!checkValidationFiles($directoryPath)) {
        return ['success' => false, 'message' => 'Directory does not contain required validation files'];
    }

    $configFiles = findConfigFiles($directoryPath);

    $files = [];
    foreach ($configFiles as $file) {
        $files[] = [
            'display_name' => $file['display_name'],
            'file_name' => $file['file_name'],
            'size' => formatBytes($file['size']),
            'modified' => date('Y-m-d H:i:s', $file['modified']),
            'is_subdirectory' => $file['is_subdirectory']
        ];
    }

    return ['success' => true, 'files' => $files];
}
?>
