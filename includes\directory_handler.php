<?php
function getDirectoryListing() {
    $directories = [];

    if (!is_dir(ROOT_PATH)) {
        return $directories;
    }

    $items = scandir(ROOT_PATH);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $fullPath = ROOT_PATH . DIRECTORY_SEPARATOR . $item;

        if (is_dir($fullPath)) {
            $foundFiles = findValidationFiles($fullPath);

            if (!empty($foundFiles)) {
                $totalSize = 0;
                $latestModified = 0;
                $fileButtons = [];

                foreach ($foundFiles as $file) {
                    $totalSize += $file['size'];
                    $latestModified = max($latestModified, $file['modified']);

                    $icon = getFileIcon($file['name']);
                    $fileButtons[] = '<button class="btn btn-sm btn-outline-primary me-1 mb-1 edit-file-btn"
                                        data-directory="' . htmlspecialchars($item) . '"
                                        data-file="' . htmlspecialchars($file['path']) . '"
                                        data-filename="' . htmlspecialchars($file['name']) . '"
                                        title="Edit ' . htmlspecialchars($file['name']) . '">
                                        <i class="' . $icon . '"></i> ' . htmlspecialchars(basename($file['name'])) . '
                                      </button>';
                }

                $directories[] = [
                    'directory' => $item,
                    'file_count' => count($foundFiles),
                    'total_size' => formatBytes($totalSize),
                    'modified' => date('Y-m-d H:i:s', $latestModified),
                    'actions' => '<div class="file-actions">' . implode('', $fileButtons) . '</div>'
                ];
            }
        }
    }

    return $directories;
}

function getDirectoryFiles($directory) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;

    if (!validatePath($directoryPath) || !is_dir($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory'];
    }

    $foundFiles = findValidationFiles($directoryPath);

    $files = [];
    foreach ($foundFiles as $file) {
        $files[] = [
            'name' => $file['name'],
            'path' => $file['path'],
            'size' => formatBytes($file['size']),
            'modified' => date('Y-m-d H:i:s', $file['modified']),
            'icon' => getFileIcon($file['name']),
            'is_subdirectory' => $file['is_subdirectory']
        ];
    }

    return ['success' => true, 'files' => $files];
}
?>
