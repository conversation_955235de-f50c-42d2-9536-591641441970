<?php
// Configuration Constants
define('ROOT_PATH', '/home/<USER>/projects');
define('VALIDATION_FILE_NAME', 'project-timeline.txt');

// Security function to validate paths
function validatePath($path) {
    $realPath = realpath($path);
    $realRootPath = realpath(ROOT_PATH);
    
    if ($realPath === false || $realRootPath === false) {
        return false;
    }
    
    return strpos($realPath, $realRootPath) === 0;
}

// Get directory listing containing validation file
function getDirectoryListing() {
    $directories = [];
    
    if (!is_dir(ROOT_PATH)) {
        return $directories;
    }
    
    $items = scandir(ROOT_PATH);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $fullPath = ROOT_PATH . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($fullPath)) {
            $validationFilePath = $fullPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
            
            if (file_exists($validationFilePath)) {
                $fileSize = filesize($validationFilePath);
                $modified = filemtime($fullPath);
                
                $directories[] = [
                    'directory' => $item,
                    'validation_size' => formatBytes($fileSize),
                    'modified' => date('Y-m-d H:i:s', $modified),
                    'actions' => '<button class="btn btn-sm btn-warning modify-btn" data-directory="' . htmlspecialchars($item) . '">Modify Config</button>'
                ];
            }
        }
    }
    
    return $directories;
}

// Get file content
function getFileContent($directory) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;
    
    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }
    
    $filePath = $directoryPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
    
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Validation file not found'];
    }
    
    $content = file_get_contents($filePath);
    
    if ($content === false) {
        return ['success' => false, 'message' => 'Unable to read file'];
    }
    
    return ['success' => true, 'content' => $content];
}

// Save file content
function saveFileContent($directory, $content) {
    $directoryPath = ROOT_PATH . DIRECTORY_SEPARATOR . $directory;
    
    if (!validatePath($directoryPath)) {
        return ['success' => false, 'message' => 'Invalid directory path'];
    }
    
    $filePath = $directoryPath . DIRECTORY_SEPARATOR . VALIDATION_FILE_NAME;
    
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Validation file not found'];
    }
    
    $result = file_put_contents($filePath, $content);
    
    if ($result === false) {
        return ['success' => false, 'message' => 'Unable to save file'];
    }
    
    return ['success' => true, 'message' => 'File saved successfully'];
}

// Format bytes to human readable format
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'list_directories':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;
            
        case 'get_file_content':
            $directory = $_POST['directory'] ?? '';
            $result = getFileContent($directory);
            echo json_encode($result);
            exit;
            
        case 'save_file':
            $directory = $_POST['directory'] ?? '';
            $content = $_POST['content'] ?? '';
            $result = saveFileContent($directory, $content);
            echo json_encode($result);
            exit;
            
        case 'refresh':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP File Manager - Project Timeline Editor</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .modify-btn {
            font-size: 0.875rem;
        }
        
        #fileContent {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .table-container {
            margin-top: 20px;
        }
        
        .header-section {
            background: #f8f9fa;
            padding: 20px 0;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="header-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-folder-open"></i>
                            Project Timeline Manager
                        </h1>
                        <p class="text-muted mb-0">Manage project timeline files in: <?php echo htmlspecialchars(ROOT_PATH); ?></p>
                    </div>
                    <div class="col-auto">
                        <button id="refreshBtn" class="btn btn-success">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="container">
            <div class="table-container">
                <table id="directoryTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="40%">Directory Name</th>
                            <th width="20%">Timeline File Size</th>
                            <th width="25%">Modified</th>
                            <th width="15%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- File Editor Modal -->
    <div class="modal fade" id="fileEditorModal" tabindex="-1" aria-labelledby="fileEditorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fileEditorModalLabel">Edit: project-timeline.txt</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body position-relative">
                    <div id="loadingOverlay" class="loading-overlay d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="fileContent" class="form-label">File Content:</label>
                        <textarea id="fileContent" class="form-control" rows="20" placeholder="Loading file content..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="saveFileBtn" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        $(document).ready(function() {
            let currentDirectory = '';
            let dataTable;

            // Initialize DataTable
            function initializeDataTable() {
                dataTable = $('#directoryTable').DataTable({
                    ajax: {
                        url: 'index.php',
                        type: 'POST',
                        data: {action: 'list_directories'},
                        dataSrc: 'data'
                    },
                    columns: [
                        {data: 'directory'},
                        {data: 'validation_size'},
                        {data: 'modified'},
                        {data: 'actions', orderable: false}
                    ],
                    order: [[0, 'asc']],
                    pageLength: 25,
                    responsive: true,
                    language: {
                        emptyTable: "No directories found containing <?php echo VALIDATION_FILE_NAME; ?>",
                        zeroRecords: "No matching directories found"
                    }
                });
            }

            // Load directories
            function loadDirectories() {
                if (dataTable) {
                    dataTable.ajax.reload();
                } else {
                    initializeDataTable();
                }
            }

            // Open file editor modal
            function openFileEditor(directory, directoryName) {
                currentDirectory = directory;

                // Update modal title
                $('#fileEditorModalLabel').text(`Edit: <?php echo VALIDATION_FILE_NAME; ?> in ${directoryName}`);

                // Show modal
                $('#fileEditorModal').modal('show');

                // Show loading overlay
                $('#loadingOverlay').removeClass('d-none');
                $('#fileContent').val('Loading...');

                // Load file content
                $.ajax({
                    url: 'index.php',
                    type: 'POST',
                    data: {
                        action: 'get_file_content',
                        directory: directory
                    },
                    dataType: 'json',
                    success: function(response) {
                        $('#loadingOverlay').addClass('d-none');

                        if (response.success) {
                            $('#fileContent').val(response.content);
                        } else {
                            $('#fileContent').val('Error loading file: ' + response.message);
                            showAlert('Error loading file: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        $('#loadingOverlay').addClass('d-none');
                        $('#fileContent').val('Error loading file content');
                        showAlert('Network error while loading file', 'danger');
                    }
                });
            }

            // Save file content
            function saveFile() {
                const content = $('#fileContent').val();

                if (!currentDirectory) {
                    showAlert('No directory selected', 'danger');
                    return;
                }

                // Show loading overlay
                $('#loadingOverlay').removeClass('d-none');

                $.ajax({
                    url: 'index.php',
                    type: 'POST',
                    data: {
                        action: 'save_file',
                        directory: currentDirectory,
                        content: content
                    },
                    dataType: 'json',
                    success: function(response) {
                        $('#loadingOverlay').addClass('d-none');

                        if (response.success) {
                            showAlert(response.message, 'success');
                            $('#fileEditorModal').modal('hide');
                            loadDirectories(); // Refresh the table
                        } else {
                            showAlert('Error saving file: ' + response.message, 'danger');
                        }
                    },
                    error: function() {
                        $('#loadingOverlay').addClass('d-none');
                        showAlert('Network error while saving file', 'danger');
                    }
                });
            }

            // Refresh data
            function refreshData() {
                loadDirectories();
                showAlert('Directory listing refreshed', 'info');
            }

            // Show alert message
            function showAlert(message, type) {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Remove existing alerts
                $('.alert').remove();

                // Add new alert at the top of the container
                $('.container').first().prepend(alertHtml);

                // Auto-dismiss after 5 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            // Event handlers
            $('#refreshBtn').on('click', refreshData);
            $('#saveFileBtn').on('click', saveFile);

            // Handle modify button clicks (delegated event)
            $(document).on('click', '.modify-btn', function() {
                const directory = $(this).data('directory');
                openFileEditor(directory, directory);
            });

            // Clear current directory when modal is hidden
            $('#fileEditorModal').on('hidden.bs.modal', function() {
                currentDirectory = '';
                $('#fileContent').val('');
            });

            // Initialize the application
            loadDirectories();
        });
    </script>
</body>
</html>
