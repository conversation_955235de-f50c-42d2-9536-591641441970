<?php
require_once 'includes/auth_config.php';
require_once 'includes/totp_auth.php';

logout();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logged Out - Project Configuration Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logout-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        
        .logout-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
        }
        
        .logout-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="logout-card">
        <div class="logout-header">
            <h2 class="mb-0">
                <i class="fas fa-check-circle"></i>
                Logged Out
            </h2>
            <p class="mb-0 mt-2">You have been successfully logged out</p>
        </div>
        
        <div class="logout-body">
            <p class="text-muted mb-4">Your session has been terminated securely.</p>
            
            <a href="login.php" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i>
                Login Again
            </a>
        </div>
    </div>
    
    <script>
        setTimeout(function() {
            window.location.href = 'login.php';
        }, 3000);
    </script>
</body>
</html>
