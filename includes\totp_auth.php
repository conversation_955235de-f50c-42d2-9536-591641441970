<?php
function base32Decode($data) {
    $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $data = strtoupper($data);
    $data = str_replace(['=', ' ', "\t", "\r", "\n"], '', $data);
    
    $binaryString = '';
    for ($i = 0; $i < strlen($data); $i++) {
        $char = $data[$i];
        $pos = strpos($alphabet, $char);
        if ($pos === false) {
            continue;
        }
        $binaryString .= sprintf('%05b', $pos);
    }
    
    $result = '';
    for ($i = 0; $i < strlen($binaryString); $i += 8) {
        $byte = substr($binaryString, $i, 8);
        if (strlen($byte) === 8) {
            $result .= chr(bindec($byte));
        }
    }
    
    return $result;
}

function generateTOTP($secret, $timeSlice = null) {
    if ($timeSlice === null) {
        $timeSlice = floor(time() / 30);
    }
    
    $secretKey = base32Decode($secret);
    $time = pack('N*', 0) . pack('N*', $timeSlice);
    $hash = hash_hmac('sha1', $time, $secretKey, true);
    $offset = ord($hash[19]) & 0xf;
    $code = (
        ((ord($hash[$offset + 0]) & 0x7f) << 24) |
        ((ord($hash[$offset + 1]) & 0xff) << 16) |
        ((ord($hash[$offset + 2]) & 0xff) << 8) |
        (ord($hash[$offset + 3]) & 0xff)
    ) % 1000000;
    
    return sprintf('%06d', $code);
}

function verifyTOTP($secret, $code, $tolerance = 1) {
    $currentTimeSlice = floor(time() / 30);
    
    for ($i = -$tolerance; $i <= $tolerance; $i++) {
        $testCode = generateTOTP($secret, $currentTimeSlice + $i);
        if ($testCode === $code) {
            return true;
        }
    }
    
    return false;
}

function generateQRCodeURL($secret, $issuer, $account) {
    $url = 'otpauth://totp/' . urlencode($issuer . ':' . $account) . 
           '?secret=' . $secret . 
           '&issuer=' . urlencode($issuer);
    
    return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($url);
}

function isAuthenticated() {
    session_start();
    
    if (!isset($_SESSION['authenticated']) || $_SESSION['authenticated'] !== true) {
        return false;
    }
    
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
        session_destroy();
        return false;
    }
    
    return true;
}

function authenticateUser($totpCode) {
    if (verifyTOTP(TOTP_SECRET, $totpCode)) {
        session_start();
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        return true;
    }
    
    return false;
}

function logout() {
    session_start();
    session_destroy();
}

function requireAuth() {
    if (!isAuthenticated()) {
        header('Location: login.php');
        exit;
    }
}
?>
