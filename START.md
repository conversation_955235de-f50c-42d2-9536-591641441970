# PHP File Manager Project

## Project Overview
Build a single-page file management system using PHP, HTML, Bootstrap CSS, and DataTables. The system lists only directories containing a specific validation file and allows editing that validation file through a modal dialog with AJAX functionality.

## Technology Stack
- **Backend**: PHP
- **Frontend**: HTML5, Bootstrap CSS (CDN), DataTables jQuery plugin (CDN)
- **Communication**: AJAX for dynamic content loading
- **Architecture**: Single-page application (SPA)
- **Dependencies**: All CSS/JS loaded from CDN - no local assets

## Core Features
1. **Filtered Directory Listing**: Display only directories in ROOT_PATH that contain VALIDATION_FILE_NAME
2. **Validation File Editing**: Modal dialog for editing the validation file with AJAX loading
3. **Real-time Refresh**: Full AJAX webpage with refresh functionality
4. **Configuration**: Constants for root path and validation file name

## Configuration Constants
```php
// Define these constants at the top of your PHP file
define('ROOT_PATH', '/path/to/your/root/directory');
define('VALIDATION_FILE_NAME', 'config.txt'); // The specific file to edit in each directory
```

## File Structure
```
project/
├── index.php (single application file with all HTML, CSS, JS, and PHP)
└── README.md
```

## CDN Resources to Include
Include these CDN links in your HTML head section:
```html
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
```

## Implementation Requirements

### 1. HTML Structure
- Single HTML page with Bootstrap layout
- DataTable container for directory listing
- Modal dialog for file editing
- Refresh button in header/toolbar

### 2. PHP Backend Functions
Create the following PHP functions:

#### `getDirectoryListing($path)`
- Scan ROOT_PATH directory for subdirectories
- Check each subdirectory for presence of VALIDATION_FILE_NAME
- Return array of only directories containing the validation file
- Include directory metadata (name, modified date, validation file size)

#### `getFileContent($filepath)`
- Read and return content of VALIDATION_FILE_NAME from specified directory
- Validate directory path is within ROOT_PATH
- Ensure VALIDATION_FILE_NAME exists in the directory

#### `saveFileContent($filepath, $content)`
- Save modified content to VALIDATION_FILE_NAME in specified directory
- Validate directory path is within ROOT_PATH
- Ensure VALIDATION_FILE_NAME exists before overwriting
- Return success/error status

#### `validatePath($path)`
- Ensure path is within ROOT_PATH boundaries
- Prevent directory traversal attacks
- Return boolean validation result

### 3. AJAX Endpoints
Structure your PHP to handle different AJAX requests:

```php
if ($_POST['action'] == 'list_directories') {
    // Return JSON array of directories containing VALIDATION_FILE_NAME
}

if ($_POST['action'] == 'get_file_content') {
    // Return VALIDATION_FILE_NAME content from specified directory
}

if ($_POST['action'] == 'save_file') {
    // Save content to VALIDATION_FILE_NAME and return status
}

if ($_POST['action'] == 'refresh') {
    // Refresh directory listing (re-scan for directories with validation file)
}
```

### 4. DataTables Configuration
- Enable server-side processing if needed
- Add custom columns: Directory Name, Validation File Size, Modified Date, Actions
- Implement search and pagination
- Custom "Modify" button in Actions column to edit VALIDATION_FILE_NAME

### 5. Modal Dialog Features
- Bootstrap modal for editing VALIDATION_FILE_NAME content
- Textarea with monospace font for config file editing
- Save/Cancel buttons
- Loading indicators during AJAX calls
- Error handling and user feedback
- Modal title shows: "Edit: [VALIDATION_FILE_NAME] in [Directory Name]"

## JavaScript Requirements

### DataTables Initialization
```javascript
$('#directoryTable').DataTable({
    ajax: {
        url: 'index.php',
        type: 'POST',
        data: {action: 'list_directories'}
    },
    columns: [
        {data: 'name'},
        {data: 'type'},
        {data: 'size'},
        {data: 'modified'},
        {data: 'actions', orderable: false}
    ]
});
```

### AJAX Functions Needed
1. `loadDirectories()` - Populate DataTable with directories containing VALIDATION_FILE_NAME
2. `openFileEditor(directoryPath, directoryName)` - Load VALIDATION_FILE_NAME content in modal
3. `saveFile()` - Save edited VALIDATION_FILE_NAME content
4. `refreshData()` - Reload directory listing (re-scan for validation files)

## Security Considerations
1. **Path Validation**: Always validate directory paths against ROOT_PATH
2. **File Restriction**: Only edit VALIDATION_FILE_NAME, no other files
3. **Directory Traversal**: Prevent access outside ROOT_PATH
4. **Input Sanitization**: Sanitize all user inputs
5. **Error Handling**: Don't expose system paths in error messages
6. **Permissions**: Check directory and file read/write permissions

## User Interface Guidelines

### Directory Table Columns
| Column | Content | Width |
|--------|---------|-------|
| Directory Name | Directory name with folder icon | 40% |
| Validation File Size | Size of VALIDATION_FILE_NAME | 20% |
| Modified | Directory last modified date/time | 25% |
| Actions | Modify button for VALIDATION_FILE_NAME | 15% |

### Modal Dialog Layout
- **Title**: "Edit: [VALIDATION_FILE_NAME] in [Directory Name]"
- **Body**: Full-width textarea with monospace font (minimum 20 rows)
- **Footer**: Save button (primary), Cancel button (secondary)
- **Loading**: Spinner overlay during AJAX operations

### Refresh Button
- Position: Top-right corner of the page
- Style: Bootstrap success button with refresh icon
- Action: Reload DataTable without page refresh

## Error Handling
1. **File Not Found**: Display user-friendly message
2. **Permission Denied**: Show appropriate error without exposing paths
3. **Network Errors**: Implement retry mechanism
4. **Validation Failures**: Highlight problematic inputs

## Performance Considerations
1. **Lazy Loading**: Only load VALIDATION_FILE_NAME content when requested
2. **Caching**: Implement appropriate caching for directory scans
3. **Large Config Files**: Warn users about editing large validation files
4. **Pagination**: Use DataTables pagination for many directories
5. **Directory Scanning**: Optimize scanning of ROOT_PATH for validation files

## Testing Checklist
- [ ] Only directories with VALIDATION_FILE_NAME are listed
- [ ] Modify button opens modal with validation file content
- [ ] File saving works correctly for VALIDATION_FILE_NAME
- [ ] Refresh button re-scans directories for validation files
- [ ] Path validation prevents unauthorized access
- [ ] Only VALIDATION_FILE_NAME can be edited (security)
- [ ] Error messages are user-friendly
- [ ] Modal dialog is responsive
- [ ] DataTables search and pagination work
- [ ] All CDN resources load correctly

## Development Steps
1. Set up single index.php file with CDN resources
2. Implement PHP function to scan for directories with VALIDATION_FILE_NAME
3. Create AJAX endpoints for directory listing and file operations
4. Initialize DataTables with filtered directory data
5. Implement modal dialog for VALIDATION_FILE_NAME editing
6. Add file save functionality for validation files only
7. Implement refresh mechanism to re-scan directories
8. Add error handling and security validation
9. Style and polish the interface
10. Test all functionality thoroughly

## Sample JSON Response Format
```json
{
    "data": [
        {
            "directory": "project_alpha",
            "validation_size": "1.2 KB",
            "modified": "2024-06-11 10:30:00",
            "actions": "<button class='btn btn-sm btn-warning modify-btn' data-directory='project_alpha'>Modify Config</button>"
        },
        {
            "directory": "project_beta", 
            "validation_size": "0.8 KB",
            "modified": "2024-06-11 09:15:00",
            "actions": "<button class='btn btn-sm btn-warning modify-btn' data-directory='project_beta'>Modify Config</button>"
        }
    ]
}
```

## Key Implementation Notes
- The system only lists directories that contain VALIDATION_FILE_NAME
- Each directory's "Modify" button edits the VALIDATION_FILE_NAME file within that directory
- The file path for editing is always: `ROOT_PATH/[directory_name]/VALIDATION_FILE_NAME`
- All Bootstrap, jQuery, and DataTables resources are loaded from CDN
- Everything is contained in a single index.php file

This README provides a complete guide for building the requested PHP file management system with all specified features.