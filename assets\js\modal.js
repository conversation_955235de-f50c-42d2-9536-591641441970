function openFileEditor(directory, fileName) {
    currentDirectory = directory;
    currentFileName = fileName;

    $('#fileEditorModalLabel').text(`Edit: ${fileName} in ${directory}`);
    $('#fileEditorModal').modal('show');
    $('#loadingOverlay').removeClass('d-none');
    $('#fileContent').val('Loading...');

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'get_file_content',
            directory: directory,
            fileName: fileName
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                $('#fileContent').val(response.content);
            } else {
                $('#fileContent').val('Error loading file: ' + response.message);
                showAlert('Error loading file: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            $('#fileContent').val('Error loading file content');
            showAlert('Network error while loading file', 'danger');
        }
    });
}

function saveFile() {
    const content = $('#fileContent').val();

    if (!currentDirectory || !currentFileName) {
        showAlert('No directory or file selected', 'danger');
        return;
    }

    $('#loadingOverlay').removeClass('d-none');

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'save_file',
            directory: currentDirectory,
            fileName: currentFileName,
            content: content
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                showAlert(response.message, 'success');
                $('#fileEditorModal').modal('hide');
                loadDirectories();
            } else {
                showAlert('Error saving file: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            showAlert('Network error while saving file', 'danger');
        }
    });
}
