function openConfigEditor(directory, fileName, displayName) {
    currentDirectory = directory;
    currentFileName = fileName;
    currentDisplayName = displayName;

    $('#configEditorModalLabel').text(`Edit: ${displayName} in ${directory}`);
    $('#configEditorModal').modal('show');
    $('#loadingOverlay').removeClass('d-none');
    $('#stringsList').empty();

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'get_config_content',
            directory: directory,
            filename: fileName
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                populateStringsList(response.strings);
                updateConfigInfo(response);
            } else {
                showAlert('Error loading config: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            showAlert('Network error while loading config', 'danger');
        }
    });
}

function populateStringsList(strings) {
    const container = $('#stringsList');
    container.empty();

    strings.forEach((str, index) => {
        addStringInput(str, index);
    });

    if (strings.length === 0) {
        addStringInput('', 0);
    }
}

function addStringInput(value = '', index = null) {
    if (index === null) {
        index = $('#stringsList .string-input-group').length;
    }

    const inputGroup = $(`
        <div class="string-input-group mb-2" data-index="${index}">
            <div class="input-group">
                <span class="input-group-text">${index + 1}</span>
                <input type="text" class="form-control string-input" value="${value}" placeholder="Enter string value">
                <button class="btn btn-outline-danger remove-string-btn" type="button">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `);

    $('#stringsList').append(inputGroup);
    updateStringIndices();
}

function updateConfigInfo(configData) {
    const configInfoHtml = `
        <div class="config-info mb-3">
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>Config:</strong> ${configData.display_name}<br>
                        <strong>File:</strong> ${configData.filename}
                    </small>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>Size:</strong> ${formatBytes(configData.size)}<br>
                        <strong>Modified:</strong> ${configData.modified}
                    </small>
                </div>
            </div>
        </div>
    `;

    $('.config-info').remove();
    $('#stringsList').before(configInfoHtml);
}

function updateStringIndices() {
    $('#stringsList .string-input-group').each(function(index) {
        $(this).attr('data-index', index);
        $(this).find('.input-group-text').text(index + 1);
    });
}

function addNewString() {
    addStringInput('');
    const newInput = $('#stringsList .string-input-group:last .string-input');
    newInput.focus();
}

function removeString(button) {
    const group = $(button).closest('.string-input-group');
    group.remove();
    updateStringIndices();

    if ($('#stringsList .string-input-group').length === 0) {
        addStringInput('', 0);
    }
}

function saveConfig() {
    if (!currentDirectory || !currentFileName) {
        showAlert('No config selected', 'danger');
        return;
    }

    const strings = [];
    $('#stringsList .string-input').each(function() {
        const value = $(this).val().trim();
        if (value !== '') {
            strings.push(value);
        }
    });

    if (strings.length === 0) {
        showAlert('Please add at least one string value', 'warning');
        return;
    }

    $('#loadingOverlay').removeClass('d-none');

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'save_config',
            directory: currentDirectory,
            filename: currentFileName,
            strings: strings
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                showAlert(`${response.display_name} saved successfully (${response.string_count} strings)`, 'success');
                $('#configEditorModal').modal('hide');
                loadDirectories();
            } else {
                showAlert('Error saving config: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            showAlert('Network error while saving config', 'danger');
        }
    });
}
