function openFileEditor(directory, fileName, displayName) {
    currentDirectory = directory;
    currentFileName = fileName;

    $('#fileEditorModalLabel').text(`Edit: ${displayName || fileName} in ${directory}`);
    $('#fileEditorModal').modal('show');
    $('#loadingOverlay').removeClass('d-none');
    $('#fileContent').val('Loading...');

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'get_file_content',
            directory: directory,
            filename: fileName
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                $('#fileContent').val(response.content);
                updateFileInfo(response);
            } else {
                $('#fileContent').val('Error loading file: ' + response.message);
                showAlert('Error loading file: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            $('#fileContent').val('Error loading file content');
            showAlert('Network error while loading file', 'danger');
        }
    });
}

function updateFileInfo(fileData) {
    const fileInfoHtml = `
        <div class="file-info mb-2">
            <small class="text-muted">
                File: ${fileData.filename} |
                Size: ${formatBytes(fileData.size)} |
                Modified: ${fileData.modified}
            </small>
        </div>
    `;

    $('.file-info').remove();
    $('#fileContent').before(fileInfoHtml);
}

function saveFile() {
    const content = $('#fileContent').val();

    if (!currentDirectory || !currentFileName) {
        showAlert('No file selected', 'danger');
        return;
    }

    $('#loadingOverlay').removeClass('d-none');

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: {
            action: 'save_file',
            directory: currentDirectory,
            filename: currentFileName,
            content: content
        },
        dataType: 'json',
        success: function(response) {
            $('#loadingOverlay').addClass('d-none');

            if (response.success) {
                showAlert(`${response.filename} saved successfully`, 'success');
                $('#fileEditorModal').modal('hide');
                loadDirectories();
            } else {
                showAlert('Error saving file: ' + response.message, 'danger');
            }
        },
        error: function() {
            $('#loadingOverlay').addClass('d-none');
            showAlert('Network error while saving file', 'danger');
        }
    });
}
