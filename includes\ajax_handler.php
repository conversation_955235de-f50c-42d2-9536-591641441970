<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'list_directories':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;

        case 'get_directory_files':
            $directory = $_POST['directory'] ?? '';
            $result = getDirectoryFiles($directory);
            echo json_encode($result);
            exit;

        case 'get_file_content':
            $directory = $_POST['directory'] ?? '';
            $fileName = $_POST['filename'] ?? '';
            $result = getFileContent($directory, $fileName);
            echo json_encode($result);
            exit;

        case 'save_file':
            $directory = $_POST['directory'] ?? '';
            $fileName = $_POST['filename'] ?? '';
            $content = $_POST['content'] ?? '';
            $result = saveFileContent($directory, $fileName, $content);
            echo json_encode($result);
            exit;

        case 'refresh':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;
    }
}
?>
