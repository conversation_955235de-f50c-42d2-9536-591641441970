<?php
require_once 'auth_config.php';
require_once 'totp_auth.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isAuthenticated()) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Authentication required', 'redirect' => 'login.php']);
        exit;
    }

    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'list_directories':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;

        case 'get_directory_config_files':
            $directory = $_POST['directory'] ?? '';
            $result = getDirectoryConfigFiles($directory);
            echo json_encode($result);
            exit;

        case 'get_config_content':
            $directory = $_POST['directory'] ?? '';
            $fileName = $_POST['filename'] ?? '';
            $result = getConfigFileContent($directory, $fileName);
            echo json_encode($result);
            exit;

        case 'save_config':
            $directory = $_POST['directory'] ?? '';
            $fileName = $_POST['filename'] ?? '';
            $stringsData = $_POST['strings'] ?? '';

            $strings = [];
            if (is_string($stringsData)) {
                $strings = array_filter(array_map('trim', explode("\n", $stringsData)));
            } elseif (is_array($stringsData)) {
                $strings = array_filter(array_map('trim', $stringsData));
            }

            $result = saveConfigFileContent($directory, $fileName, $strings);
            echo json_encode($result);
            exit;

        case 'refresh':
            $directories = getDirectoryListing();
            echo json_encode(['data' => $directories]);
            exit;
    }
}
?>
